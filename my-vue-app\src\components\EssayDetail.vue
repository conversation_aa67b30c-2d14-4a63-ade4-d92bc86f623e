<template>
  <div class="essay-detail">
    <!-- 状态栏 -->
    <div class="status-bar">
      <div class="status-left">
        <div class="signal-bars">
          <div class="bar"></div>
          <div class="bar"></div>
          <div class="bar"></div>
          <div class="bar"></div>
        </div>
      </div>
      <div class="status-right">
        <div class="battery-indicator">
          <div class="battery-level"></div>
        </div>
      </div>
    </div>

    <!-- 头部导航 -->
    <div class="header">
      <div class="back-button" @click="goBack">
        <img :src="arrowLeftDetail" alt="返回" />
      </div>
      <div class="header-title">详情</div>
    </div>

    <!-- 标签页导航 -->
    <div class="tab-navigation">
      <div class="tab-item" :class="{ active: currentTab === 'review' }" @click="switchTab('review')">
        <span>作文点评</span>
      </div>
      <div class="tab-item" :class="{ active: currentTab === 'requirements' }" @click="switchTab('requirements')">
        <span>作文要求</span>
      </div>
      <div class="tab-item" :class="{ active: currentTab === 'report' }" @click="switchTab('report')">
        <span>作文报告</span>
        <div class="tab-indicator" v-if="currentTab === 'report'"></div>
      </div>
      <div class="tab-item" :class="{ active: currentTab === 'sample' }" @click="switchTab('sample')">
        <span>润色范文</span>
      </div>
    </div>

    <!-- 作文信息卡片 -->
    <div class="essay-info-card">
      <div class="essay-tags">
        <span class="tag">第一单元</span>
        <span class="tag">单元作文</span>
        <span class="tag">全命题</span>
      </div>
      <div class="essay-title">我的植物朋友</div>
      <div class="essay-score">
        <span class="score-number">28</span>
        <span class="score-unit">分</span>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="content-area">
      <!-- 作文点评内容 -->
      <div v-if="currentTab === 'review'" class="review-content">
        <!-- 点评标题 -->
        <div class="section-title">点评</div>

      <!-- 思想与中心 -->
      <div class="review-section">
        <div class="section-header">
          <span class="section-name">思想与中心</span>
          <div class="star-rating">
            <img :src="starFilled" alt="星星" class="star" />
            <img :src="starFilled" alt="星星" class="star" />
            <img :src="starFilled" alt="星星" class="star" />
            <img :src="starFilled" alt="星星" class="star" />
          </div>
        </div>
        <div class="review-label">评语</div>
        <div class="review-text">文章紧扣'未来的城市生活'主题，通过多个方面描绘了未来城市的科技发展和生活变化，中心思想明确。</div>
      </div>

      <!-- 内容 -->
      <div class="review-section">
        <div class="section-header">
          <span class="section-name">内容</span>
          <div class="star-rating">
            <img :src="starFilled" alt="星星" class="star" />
            <img :src="starFilled" alt="星星" class="star" />
            <img :src="starFilled" alt="星星" class="star" />
            <img :src="starFilled" alt="星星" class="star" />
          </div>
        </div>
        <div class="review-label">评语</div>
        <div class="review-text">内容具体详细，涵盖了建筑、交通、环保、教育和医疗等多个领域，展现了未来城市生活的全面图景。</div>
      </div>

      <!-- 结构 -->
      <div class="review-section">
        <div class="section-header">
          <span class="section-name">结构</span>
          <div class="star-rating">
            <img :src="starFilled" alt="星星" class="star" />
            <img :src="starFilled" alt="星星" class="star" />
            <img :src="starFilled" alt="星星" class="star" />
            <img :src="starFilled" alt="星星" class="star" />
          </div>
        </div>
        <div class="review-label">评语</div>
        <div class="review-text">文章结构较为清晰，但部分段落之间的过渡不够自然，详略处理有待加强。</div>
      </div>

      <!-- 语言 -->
      <div class="review-section">
        <div class="section-header">
          <span class="section-name">语言</span>
          <div class="star-rating">
            <img :src="starFilled" alt="星星" class="star" />
            <img :src="starFilled" alt="星星" class="star" />
            <img :src="starFilled" alt="星星" class="star" />
            <img :src="starFilled" alt="星星" class="star" />
          </div>
        </div>
        <div class="review-label">评语</div>
        <div class="review-text">语句通顺</div>
      </div>

      <!-- 总结评语 -->
      <div class="review-section">
        <div class="section-name">总结评语</div>
        <div class="review-text summary">本文围绕'未来的城市生活'这一主题，从建筑、交通、环保、教育和医疗等多个方面展开，描绘了一个科技高度发达的未来城市图景。文章内容具体，涵盖了多个领域，展现了未来城市生活的全面变化。结构上，文章层次较为清晰，但部分段落之间的过渡不够自然，详略处理有待加强。语言方面，文章语句基本通顺，但存在个别错别字和标点符号使用不当的问题，修辞手法的运用也较为有限。总体而言，文章紧扣主题，中心思想明确，内容具体，结构较为清晰，语言基本流畅，但仍有提升空间。</div>
      </div>

      <!-- 字数不足 -->
      <div class="review-section">
        <div class="section-header">
          <span class="section-name">字数不足</span>
          <span class="deduction">扣2分</span>
        </div>
        <div class="word-count">
          <span class="label">总字数</span>
          <span class="count">100字</span>
        </div>
      </div>

      <!-- 不足之处标题 -->
      <div class="section-title">不足之处</div>

      <!-- 错别字 -->
      <div class="review-section">
        <div class="section-header">
          <span class="section-name">错别字</span>
          <span class="count-info">共10处</span>
        </div>
        <div class="error-list">
          <div class="error-row">
            <span class="error-item">1.棋中（其中）</span>
            <span class="error-item">2.坐号（座号）</span>
            <span class="error-item">3.坐号（座号）</span>
            <span class="error-item">4.坐号（座号）</span>
          </div>
          <div class="error-row">
            <span class="error-item">5.棋中（其中）</span>
            <span class="error-item">6.坐号（座号）</span>
            <span class="error-item">7.坐号（座号）</span>
            <span class="error-item">8.坐号（座号）</span>
          </div>
          <div class="error-row">
            <span class="error-item">9.棋中（其中）</span>
            <span class="error-item">10.坐号（座号）</span>
          </div>
        </div>
      </div>

      <!-- 滥用拼音 -->
      <div class="review-section">
        <div class="section-header">
          <span class="section-name">滥用拼音</span>
          <span class="count-info">共10处</span>
        </div>
        <div class="error-list">
          <div class="error-row">
            <span class="error-item">1.qi（其）</span>
            <span class="error-item">2.zuo（座）</span>
            <span class="error-item">3.zuo（座）</span>
            <span class="error-item">4.zuo（座）</span>
          </div>
          <div class="error-row">
            <span class="error-item">5.zuo（座）</span>
            <span class="error-item">6.zuo（座）</span>
            <span class="error-item">7.zuo（座）</span>
            <span class="error-item">8.zuo（座）</span>
          </div>
          <div class="error-row">
            <span class="error-item">9.zuo（座）</span>
            <span class="error-item">10.zuo（座）</span>
          </div>
        </div>
      </div>

        <!-- 最后批改时间 -->
        <div class="review-time">最后批改时间：2025.07.16  15:46</div>
      </div>

      <!-- 作文报告内容 -->
      <div v-if="currentTab === 'report'" class="report-content">
        <div class="report-container">
          <!-- 这里将显示作文报告的内容 -->
          <div class="report-placeholder">
            <p>作文报告内容正在加载中...</p>
          </div>
        </div>
      </div>

      <!-- 作文要求内容 -->
      <div v-if="currentTab === 'requirements'" class="requirements-content">
        <div class="requirements-container">
          <!-- 这里将显示作文要求的内容 -->
          <div class="requirements-placeholder">
            <p>作文要求内容正在加载中...</p>
          </div>
        </div>
      </div>

      <!-- 润色范文内容 -->
      <div v-if="currentTab === 'sample'" class="sample-content">
        <div class="sample-container">
          <!-- 这里将显示润色范文的内容 -->
          <div class="sample-placeholder">
            <p>润色范文内容正在加载中...</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import arrowLeftDetail from '../assets/images/arrow-left.svg'
import starFilled from '../assets/images/star-filled.svg'

export default {
  name: 'EssayDetail',
  emits: ['go-back'],
  data() {
    return {
      arrowLeftDetail,
      starFilled,
      currentTab: 'report'
    }
  },
  methods: {
    goBack() {
      this.$emit('go-back')
    },
    switchTab(tab) {
      this.currentTab = tab
      console.log('切换到标签页:', tab)
    }
  }
}
</script>

<style scoped>
.essay-detail {
  width: 375px;
  height: 1426px;
  background: #FFFFFF;
  position: relative;
  margin: 0 auto;
  box-shadow: 0px 3px 6px 0px rgba(18, 15, 40, 0.12);
  font-family: 'Inter', sans-serif;
  overflow-y: auto;
}

/* 状态栏样式 */
.status-bar {
  width: 100%;
  height: 40px;
  background: transparent;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 12px;
  box-sizing: border-box;
}

.status-left {
  display: flex;
  align-items: center;
}

.signal-bars {
  display: flex;
  gap: 2px;
  align-items: flex-end;
}

.bar {
  width: 2.55px;
  background: #000000;
}

.bar:nth-child(1) { height: 3.4px; }
.bar:nth-child(2) { height: 5.53px; }
.bar:nth-child(3) { height: 8.51px; }
.bar:nth-child(4) { height: 10.21px; }

.status-right {
  display: flex;
  align-items: center;
}

.battery-indicator {
  width: 20.28px;
  height: 10.06px;
  border: 1px solid #000000;
  border-radius: 2px;
  position: relative;
  opacity: 0.35;
}

.battery-level {
  width: 17.87px;
  height: 7.66px;
  background: #000000;
  position: absolute;
  top: 1.2px;
  left: 1.21px;
}

/* 头部导航 */
.header {
  height: 32px;
  display: flex;
  align-items: center;
  padding: 0 24px;
  position: relative;
}

.back-button {
  width: 24px;
  height: 24px;
  cursor: pointer;
}

.back-button img {
  width: 100%;
  height: 100%;
}

.header-title {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-size: 16px;
  line-height: 26px;
  color: #323842;
  font-weight: 400;
}

/* 标签页导航 */
.tab-navigation {
  height: 40px;
  display: flex;
  padding: 0 23px;
  background: transparent;
}

.tab-item {
  width: 60px;
  height: 40px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  position: relative;
}

.tab-item span {
  font-size: 11px;
  line-height: 18px;
  color: #565E6C;
  font-weight: 400;
}

.tab-item.active span {
  color: #636AE8;
  font-weight: 700;
}

.tab-indicator {
  position: absolute;
  bottom: 0;
  width: 60px;
  height: 4px;
  background: #636AE8;
}

/* 作文信息卡片 */
.essay-info-card {
  height: 65px;
  background: #F8F9FA;
  padding: 11px 17px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;
}

.essay-tags {
  display: flex;
  gap: 12px;
}

.tag {
  font-size: 11px;
  line-height: 18px;
  color: #323842;
  font-weight: 400;
}

.essay-title {
  font-size: 11px;
  line-height: 18px;
  color: #323842;
  font-weight: 400;
}

.essay-score {
  position: absolute;
  top: 15px;
  right: 17px;
  display: flex;
  align-items: baseline;
  gap: 5px;
}

.score-number {
  font-size: 24px;
  line-height: 36px;
  color: #DE3B40;
  font-weight: 400;
}

.score-unit {
  font-size: 12px;
  line-height: 20px;
  color: #DE3B40;
  font-weight: 400;
}

/* 内容区域 */
.content-area {
  background: #FFFFFF;
}

/* 点评内容 */
.review-content {
  padding: 10px 0;
  background: #FFFFFF;
}

.section-title {
  font-size: 14px;
  line-height: 22px;
  color: #171A1F;
  font-weight: 400;
  padding: 0 17px;
  margin-bottom: 10px;
}

.review-section {
  background: #F8F9FA;
  padding: 11px 17px;
  margin-bottom: 10px;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.section-name {
  font-size: 11px;
  line-height: 18px;
  color: #323842;
  font-weight: 400;
}

.star-rating {
  display: flex;
  gap: 0;
}

.star {
  width: 18px;
  height: 18px;
}

.review-label {
  font-size: 11px;
  line-height: 18px;
  color: #323842;
  font-weight: 400;
  margin-bottom: 8px;
}

.review-text {
  font-size: 11px;
  line-height: 18px;
  color: #323842;
  font-weight: 400;
}

.review-text.summary {
  margin-top: 0;
}

.deduction {
  font-size: 11px;
  line-height: 18px;
  color: #323842;
  font-weight: 400;
}

.word-count {
  display: flex;
  gap: 19px;
  margin-top: 8px;
}

.word-count .label,
.word-count .count {
  font-size: 11px;
  line-height: 18px;
  color: #323842;
  font-weight: 400;
}

.count-info {
  font-size: 11px;
  line-height: 18px;
  color: #323842;
  font-weight: 400;
}

.error-list {
  margin-top: 8px;
}

.error-row {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
}

.error-row:last-child {
  margin-bottom: 0;
}

.error-item {
  font-size: 11px;
  line-height: 18px;
  color: #323842;
  font-weight: 400;
  flex: 1;
}

.review-time {
  font-size: 11px;
  line-height: 18px;
  color: #323842;
  font-weight: 400;
  padding: 10px 17px;
}

/* 作文报告内容 */
.report-content {
  padding: 20px;
  background: #FFFFFF;
  min-height: 400px;
}

.report-container {
  width: 100%;
}

.report-placeholder {
  text-align: center;
  padding: 50px 20px;
  color: #565E6C;
  font-size: 14px;
}

/* 作文要求内容 */
.requirements-content {
  padding: 20px;
  background: #FFFFFF;
  min-height: 400px;
}

.requirements-container {
  width: 100%;
}

.requirements-placeholder {
  text-align: center;
  padding: 50px 20px;
  color: #565E6C;
  font-size: 14px;
}

/* 润色范文内容 */
.sample-content {
  padding: 20px;
  background: #FFFFFF;
  min-height: 400px;
}

.sample-container {
  width: 100%;
}

.sample-placeholder {
  text-align: center;
  padding: 50px 20px;
  color: #565E6C;
  font-size: 14px;
}
</style>
